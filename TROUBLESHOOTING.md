# 故障排除指南

> 常见问题和解决方案

## 🚨 部署相关问题

### 1. Wrangler登录失败

**问题**: `wrangler login` 命令失败

**可能原因**:
- 网络连接问题
- Cloudflare服务异常
- 浏览器阻止弹窗

**解决方案**:
```bash
# 方法1: 使用API Token
wrangler auth login

# 方法2: 手动设置API Token
export CLOUDFLARE_API_TOKEN="your_api_token"

# 方法3: 使用全局API Key
export CLOUDFLARE_EMAIL="your_email"
export CLOUDFLARE_API_KEY="your_global_api_key"
```

### 2. 部署失败

**问题**: `wrangler deploy` 命令失败

**常见错误信息**:
```
Error: A request to the Cloudflare API failed
```

**解决方案**:
1. 检查网络连接
2. 验证API权限
3. 检查代码语法错误
4. 查看详细错误信息：
```bash
wrangler deploy --verbose
```

### 3. 环境变量未生效

**问题**: 设置的环境变量在Worker中无法访问

**解决方案**:
```bash
# 检查环境变量是否正确设置
wrangler secret list

# 重新设置环境变量
wrangler secret put NETEASE_COOKIE
wrangler secret put QQ_COOKIE

# 重新部署
wrangler deploy
```

## 🎵 功能相关问题

### 1. Cookie无效错误

**问题**: API返回401错误，提示Cookie无效

**错误信息**:
```json
{
  "status": 401,
  "error": "网易云Cookie无效或未配置"
}
```

**解决方案**:
1. **重新获取Cookie**:
   - 清除浏览器缓存
   - 重新登录音乐平台
   - 复制最新的Cookie

2. **验证Cookie格式**:
   ```javascript
   // 网易云Cookie应包含MUSIC_U字段
   // QQ音乐Cookie应包含uin和skey字段
   ```

3. **检查会员状态**:
   - 确保账户有有效的会员权限
   - 网易云需要黑胶会员
   - QQ音乐需要绿钻会员

### 2. 解析失败

**问题**: 歌曲解析返回空结果或错误

**可能原因**:
- 歌曲ID无效
- 歌曲被下架
- 地区限制
- 版权保护

**解决方案**:
1. **验证歌曲ID**:
   ```bash
   # 测试有效的歌曲ID
   curl "https://your-worker.workers.dev/api/netease/song" \
     -H "Content-Type: application/json" \
     -d '{"url":"186016","level":"standard","type":"json"}'
   ```

2. **尝试不同音质**:
   - 从`standard`开始测试
   - 逐步提升到`lossless`、`hires`等

3. **检查歌曲状态**:
   - 在官方平台确认歌曲是否可用
   - 尝试其他歌曲进行对比测试

### 3. 搜索无结果

**问题**: 搜索API返回空数组

**解决方案**:
1. **检查关键词**:
   - 使用常见的歌手或歌曲名
   - 避免特殊字符
   - 尝试不同的关键词组合

2. **调整搜索参数**:
   ```bash
   # 增加返回数量
   curl "https://your-worker.workers.dev/api/netease/search?keywords=周杰伦&limit=20"
   ```

## 🌐 网络相关问题

### 1. 请求超时

**问题**: API请求超时

**错误信息**:
```json
{
  "status": 500,
  "error": "请求超时"
}
```

**解决方案**:
1. **检查网络连接**
2. **重试请求**
3. **联系技术支持**

### 2. CORS错误

**问题**: 浏览器控制台显示CORS错误

**解决方案**:
API已配置CORS头，如果仍有问题：
1. 检查请求URL是否正确
2. 确认使用HTTPS协议
3. 尝试使用服务端请求而非浏览器直接请求

### 3. 域名解析问题

**问题**: 自定义域名无法访问

**解决方案**:
1. **检查DNS设置**:
   ```bash
   nslookup your-domain.com
   ```

2. **验证Cloudflare配置**:
   - 确认域名已添加到Cloudflare
   - 检查DNS记录是否正确
   - 验证SSL证书状态

## 🔧 性能相关问题

### 1. 响应缓慢

**问题**: API响应时间过长

**解决方案**:
1. **检查Worker日志**:
   ```bash
   wrangler tail
   ```

2. **优化请求**:
   - 减少并发请求数量
   - 使用缓存减少重复请求
   - 选择合适的音质等级

### 2. 内存不足

**问题**: Worker因内存不足而失败

**解决方案**:
1. **优化代码**:
   - 减少内存使用
   - 及时释放不需要的变量
   - 使用流式处理大数据

2. **分批处理**:
   - 将大型歌单分批解析
   - 限制单次请求的数据量

## 📱 Web界面问题

### 1. 界面无法加载

**问题**: 访问根路径时页面空白或加载失败

**解决方案**:
1. **检查浏览器控制台**:
   - 查看JavaScript错误
   - 检查网络请求状态

2. **清除浏览器缓存**:
   - 强制刷新页面 (Ctrl+F5)
   - 清除浏览器缓存和Cookie

3. **检查Worker状态**:
   ```bash
   curl "https://your-worker.workers.dev/health"
   ```

### 2. 功能按钮无响应

**问题**: 点击搜索或解析按钮无反应

**解决方案**:
1. **检查输入内容**:
   - 确保输入框不为空
   - 验证输入格式是否正确

2. **查看浏览器控制台**:
   - 检查JavaScript错误
   - 查看网络请求状态

## 🔍 调试技巧

### 1. 启用详细日志

```bash
# 查看实时日志
wrangler tail

# 查看特定时间段的日志
wrangler tail --since 1h
```

### 2. 本地调试

```bash
# 启动本地开发服务器
wrangler dev

# 指定端口
wrangler dev --port 8080
```

### 3. 测试API端点

```bash
# 健康检查
curl "https://your-worker.workers.dev/health"

# 测试搜索
curl "https://your-worker.workers.dev/api/netease/search?keywords=test"

# 测试解析
curl -X POST "https://your-worker.workers.dev/api/netease/song" \
  -H "Content-Type: application/json" \
  -d '{"url":"186016","level":"standard","type":"json"}'
```

## 📞 获取帮助

### 1. 检查日志

首先查看Cloudflare Workers的实时日志：
```bash
wrangler tail
```

### 2. 验证配置

检查环境变量和配置：
```bash
wrangler secret list
wrangler status
```

### 3. 联系支持

如果问题仍未解决：
1. 收集错误信息和日志
2. 记录重现步骤
3. 提交GitHub Issue
4. 包含以下信息：
   - 错误信息
   - 请求参数
   - 环境信息
   - 重现步骤

## 🔄 常见解决流程

### 问题诊断流程
1. **确认问题类型** (部署/功能/性能)
2. **查看错误日志** (`wrangler tail`)
3. **检查配置** (环境变量/域名/权限)
4. **测试基础功能** (健康检查/简单API)
5. **逐步排查** (从简单到复杂)
6. **寻求帮助** (文档/社区/技术支持)

### 快速修复检查清单
- [ ] Cookie是否有效且有会员权限
- [ ] 环境变量是否正确设置
- [ ] Worker是否成功部署
- [ ] 网络连接是否正常
- [ ] 输入参数是否正确
- [ ] 浏览器缓存是否已清除
