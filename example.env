# 音乐解析工具 - 环境变量配置示例
# 复制此文件为 .env 并填入实际值

# ===========================================
# 网易云音乐配置
# ===========================================

# 网易云音乐Cookie（必需）
# 获取方法：
# 1. 登录网易云音乐网页版 (https://music.163.com/)
# 2. 确保账户有黑胶会员权限
# 3. 打开浏览器开发者工具 (F12)
# 4. 在Network标签中找到任意请求，复制完整Cookie值
# 示例格式：MUSIC_U=xxx; __csrf=xxx; _iuqxldmzr_=xxx; _ntes_nnid=xxx; _ntes_nuid=xxx;
NETEASE_COOKIE="your_netease_cookie_here"

# ===========================================
# QQ音乐配置
# ===========================================

# QQ音乐Cookie（必需）
# 获取方法：
# 1. 登录QQ音乐网页版 (https://y.qq.com/)
# 2. 确保账户有会员权限
# 3. 打开浏览器开发者工具 (F12)
# 4. 在Network标签中找到任意请求，复制完整Cookie值
# 示例格式：uin=xxx; skey=xxx; p_skey=xxx; pt4_token=xxx; p_uin=xxx;
QQ_COOKIE="your_qq_cookie_here"

# ===========================================
# 应用配置
# ===========================================

# 应用版本号（可选）
APP_VERSION="1.0.0"

# ===========================================
# Cloudflare Workers 部署配置
# ===========================================

# Worker名称（用于wrangler.toml）
WORKER_NAME="music-parser"

# 自定义域名（可选）
# CUSTOM_DOMAIN="music.yourdomain.com"

# ===========================================
# 安全配置（可选）
# ===========================================

# 允许的IP地址列表（逗号分隔，可选）
# ALLOWED_IPS="***********,********"

# API密钥（如果需要API认证，可选）
# API_KEY="your_api_key_here"

# ===========================================
# 功能开关（可选）
# ===========================================

# 是否启用网易云音乐功能（默认：true）
# ENABLE_NETEASE="true"

# 是否启用QQ音乐功能（默认：true）
# ENABLE_QQ="true"

# 是否启用Web界面（默认：true）
# ENABLE_WEB_UI="true"

# 是否启用API文档（默认：true）
# ENABLE_API_DOCS="true"

# ===========================================
# 缓存配置（可选）
# ===========================================

# 搜索结果缓存时间（秒，默认：300）
# SEARCH_CACHE_TTL="300"

# 歌曲信息缓存时间（秒，默认：3600）
# SONG_CACHE_TTL="3600"

# ===========================================
# 日志配置（可选）
# ===========================================

# 日志级别（debug, info, warn, error，默认：info）
# LOG_LEVEL="info"

# 是否启用详细日志（默认：false）
# ENABLE_VERBOSE_LOGGING="false"

# ===========================================
# 性能配置（可选）
# ===========================================

# 请求超时时间（毫秒，默认：10000）
# REQUEST_TIMEOUT="10000"

# 最大并发请求数（默认：10）
# MAX_CONCURRENT_REQUESTS="10"

# ===========================================
# 开发环境配置
# ===========================================

# 开发模式（默认：false）
# DEV_MODE="false"

# 调试模式（默认：false）
# DEBUG_MODE="false"

# 本地开发端口（默认：8787）
# DEV_PORT="8787"

# ===========================================
# 监控配置（可选）
# ===========================================

# 是否启用性能监控（默认：false）
# ENABLE_MONITORING="false"

# 监控数据上报URL（可选）
# MONITORING_URL="https://your-monitoring-service.com/api/metrics"

# ===========================================
# 备注说明
# ===========================================

# 1. Cookie获取注意事项：
#    - 确保Cookie是最新的，过期Cookie会导致解析失败
#    - 网易云需要黑胶会员Cookie才能解析高音质
#    - QQ音乐需要会员Cookie才能解析高音质
#    - Cookie包含敏感信息，请妥善保管

# 2. 部署方式选择：
#    - 推荐使用 wrangler secret 命令设置敏感信息
#    - 也可以在Cloudflare控制台直接设置环境变量
#    - 避免在代码仓库中提交真实的Cookie值

# 3. 安全建议：
#    - 定期更换Cookie
#    - 使用强密码保护账户
#    - 启用两步验证
#    - 监控异常访问

# 4. 性能优化：
#    - 根据实际需求调整缓存时间
#    - 合理设置并发请求数
#    - 监控Workers的CPU和内存使用情况

# 5. 故障排除：
#    - 如果解析失败，首先检查Cookie是否有效
#    - 查看Cloudflare Workers的实时日志
#    - 确认账户有足够的会员权限
