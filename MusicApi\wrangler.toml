name = "music-parser"
main = "src/index.js"
compatibility_date = "2024-01-01"

# 可选：在此处配置环境变量（也可以在CF控制台设置）
[vars]
# 网易云音乐Cookie配置（需要黑胶会员Cookie）
# NETEASE_COOKIE = "your_netease_cookie_here"
# QQ音乐Cookie配置（需要会员Cookie）
# QQ_COOKIE = "your_qq_cookie_here"
# 应用版本
APP_VERSION = "1.0.0"

# 生产环境配置
[env.production]
name = "music-parser-prod"
# 生产环境可以使用不同的Cookie配置
# [env.production.vars]
# NETEASE_COOKIE = "production_netease_cookie"
# QQ_COOKIE = "production_qq_cookie"

# 测试环境配置
[env.staging]
name = "music-parser-staging"
# [env.staging.vars]
# NETEASE_COOKIE = "staging_netease_cookie"
# QQ_COOKIE = "staging_qq_cookie"

# 开发环境配置
[env.development]
name = "music-parser-dev"

# Workers配置
[build]
command = ""

# 兼容性设置
compatibility_flags = []

# 资源限制
[limits]
# CPU时间限制（毫秒）
cpu_ms = 50000
