# 音乐解析工具 - API文档

> 完整的API接口说明和使用示例

## 📖 概述

本API提供网易云音乐和QQ音乐的解析服务，支持歌曲搜索、单曲解析、歌单解析、专辑解析等功能。

**基础URL**: `https://your-worker.your-subdomain.workers.dev`

## 🔐 认证说明

API使用服务端配置的Cookie进行认证，客户端无需提供认证信息。

## 📊 通用响应格式

### 成功响应
```json
{
  "status": 200,
  "data": { ... },
  "timestamp": "2025-01-01T00:00:00.000Z"
}
```

### 错误响应
```json
{
  "status": 400,
  "error": "错误信息",
  "timestamp": "2025-01-01T00:00:00.000Z"
}
```

## 🎵 网易云音乐API

### 1. 歌曲搜索

**接口**: `GET /api/netease/search`

**参数**:
- `keywords` (必需): 搜索关键词
- `limit` (可选): 返回数量，默认10，最大50

**示例**:
```bash
GET /api/netease/search?keywords=周杰伦&limit=5
```

**响应**:
```json
{
  "status": 200,
  "result": [
    {
      "id": "186016",
      "name": "不能说的秘密",
      "artists": "周杰伦",
      "album": "不能说的秘密 电影原声带",
      "picUrl": "https://p3.music.126.net/..."
    }
  ]
}
```

### 2. 单曲解析

**接口**: `POST /api/netease/song`

**参数**:
- `url` (必需): 歌曲ID或网易云链接
- `level` (必需): 音质等级
- `type` (必需): 返回类型

**音质等级**:
- `standard`: 标准音质
- `exhigh`: 极高音质
- `lossless`: 无损音质
- `hires`: Hi-Res音质
- `sky`: 沉浸环绕声
- `jyeffect`: 高清环绕声
- `jymaster`: 超清母带

**返回类型**:
- `json`: JSON格式数据
- `text`: HTML格式文本
- `down`: 直接重定向到下载链接

**示例**:
```bash
POST /api/netease/song
Content-Type: application/json

{
  "url": "186016",
  "level": "lossless",
  "type": "json"
}
```

**响应**:
```json
{
  "status": 200,
  "name": "不能说的秘密",
  "pic": "https://p3.music.126.net/...",
  "ar_name": "周杰伦",
  "al_name": "不能说的秘密 电影原声带",
  "level": "无损音质",
  "size": "45.2MB",
  "url": "https://music.163.com/...",
  "lyric": "[00:00.000] 作词 : 周杰伦...",
  "tlyric": null
}
```

### 3. 歌单解析

**接口**: `GET /api/netease/playlist`

**参数**:
- `id` (必需): 歌单ID

**示例**:
```bash
GET /api/netease/playlist?id=123456789
```

**响应**:
```json
{
  "status": 200,
  "playlist": {
    "id": "123456789",
    "name": "华语经典",
    "coverImgUrl": "https://p3.music.126.net/...",
    "creator": "用户名",
    "trackCount": 100,
    "description": "歌单描述",
    "tracks": [
      {
        "id": "186016",
        "name": "不能说的秘密",
        "artists": "周杰伦",
        "album": "不能说的秘密 电影原声带",
        "picUrl": "https://p3.music.126.net/..."
      }
    ]
  }
}
```

### 4. 专辑解析

**接口**: `GET /api/netease/album`

**参数**:
- `id` (必需): 专辑ID

**示例**:
```bash
GET /api/netease/album?id=18905
```

**响应**:
```json
{
  "status": 200,
  "album": {
    "id": "18905",
    "name": "不能说的秘密 电影原声带",
    "coverImgUrl": "https://p3.music.126.net/...",
    "artist": "周杰伦",
    "publishTime": 1194019200000,
    "description": "专辑描述",
    "songs": [
      {
        "id": "186016",
        "name": "不能说的秘密",
        "artists": "周杰伦",
        "album": "不能说的秘密 电影原声带",
        "picUrl": "https://p3.music.126.net/..."
      }
    ]
  }
}
```

## 🎶 QQ音乐API

### 歌曲解析

**接口**: `GET /api/qq/song`

**参数**:
- `url` (必需): QQ音乐歌曲链接

**示例**:
```bash
GET /api/qq/song?url=https://y.qq.com/n/ryqq/songDetail/001JdWaS1KWsEy
```

**响应**:
```json
{
  "song": {
    "name": "不能说的秘密",
    "album": "不能说的秘密",
    "singer": "周杰伦",
    "pic": "https://y.qq.com/music/photo_new/...",
    "mid": "001JdWaS1KWsEy",
    "id": "654321"
  },
  "lyric": {
    "lyric": "[00:00.000] 作词 : 周杰伦...",
    "tylyric": ""
  },
  "music_urls": {
    "128": {
      "url": "https://ws.stream.qqmusic.qq.com/...",
      "bitrate": "128kbps"
    },
    "320": {
      "url": "https://ws.stream.qqmusic.qq.com/...",
      "bitrate": "320kbps"
    },
    "flac": {
      "url": "https://ws.stream.qqmusic.qq.com/...",
      "bitrate": "FLAC"
    }
  }
}
```

## 🔧 系统接口

### 健康检查

**接口**: `GET /health`

**响应**:
```json
{
  "status": "ok",
  "version": "1.0.0",
  "timestamp": "2025-01-01T00:00:00.000Z",
  "services": {
    "netease": true,
    "qq": true
  }
}
```

### API信息

**接口**: `GET /info`

**响应**:
```json
{
  "message": "音乐解析工具 API",
  "version": "1.0.0",
  "endpoints": {
    "web": "/",
    "health": "/health",
    "netease": {
      "song": "/api/netease/song",
      "search": "/api/netease/search",
      "playlist": "/api/netease/playlist",
      "album": "/api/netease/album"
    },
    "qq": {
      "song": "/api/qq/song"
    }
  }
}
```

## 🔄 向后兼容

为保持与原版本的兼容性，以下旧版API路径仍然有效：

| 旧路径 | 新路径 | 说明 |
|--------|--------|------|
| `/Song_V1` | `/api/netease/song` | 网易云单曲解析 |
| `/Search` | `/api/netease/search` | 网易云搜索 |
| `/Playlist` | `/api/netease/playlist` | 网易云歌单解析 |
| `/Album` | `/api/netease/album` | 网易云专辑解析 |
| `/song` | `/api/qq/song` | QQ音乐解析 |

## 📝 使用示例

### JavaScript/Node.js
```javascript
// 搜索歌曲
const searchResponse = await fetch('https://your-worker.workers.dev/api/netease/search?keywords=周杰伦');
const searchData = await searchResponse.json();

// 解析单曲
const parseResponse = await fetch('https://your-worker.workers.dev/api/netease/song', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    url: '186016',
    level: 'lossless',
    type: 'json'
  })
});
const parseData = await parseResponse.json();
```

### Python
```python
import requests

# 搜索歌曲
search_response = requests.get(
    'https://your-worker.workers.dev/api/netease/search',
    params={'keywords': '周杰伦', 'limit': 5}
)
search_data = search_response.json()

# 解析单曲
parse_response = requests.post(
    'https://your-worker.workers.dev/api/netease/song',
    json={
        'url': '186016',
        'level': 'lossless',
        'type': 'json'
    }
)
parse_data = parse_response.json()
```

### cURL
```bash
# 搜索歌曲
curl "https://your-worker.workers.dev/api/netease/search?keywords=周杰伦&limit=5"

# 解析单曲
curl -X POST "https://your-worker.workers.dev/api/netease/song" \
  -H "Content-Type: application/json" \
  -d '{"url":"186016","level":"lossless","type":"json"}'
```

## ⚠️ 注意事项

1. **Cookie有效性**: 确保配置的Cookie有效且具有相应的会员权限
2. **请求频率**: 建议控制请求频率，避免被平台限制
3. **版权声明**: 本工具仅供学习和个人使用，请遵守相关平台的使用条款
4. **数据缓存**: API响应可能会被缓存，实际数据可能有延迟

## 🐛 错误码说明

| 状态码 | 说明 | 解决方案 |
|--------|------|----------|
| 400 | 请求参数错误 | 检查参数格式和必需参数 |
| 401 | Cookie无效或未配置 | 更新Cookie配置 |
| 404 | 接口不存在 | 检查API路径 |
| 500 | 服务器内部错误 | 查看错误日志，联系技术支持 |

## 📞 技术支持

- 📖 [部署指南](./DEPLOY.md)
- 🔧 [故障排除](./TROUBLESHOOTING.md)
- 💬 [GitHub Issues](https://github.com/your-repo/issues)
