# 项目设置指南

> 如何将源文件整理到MusicApi目录中

## 📁 文件结构整理

由于项目文件分散在不同目录中，您需要将所有源文件复制到 `MusicApi` 目录中。

### 当前文件分布

```
项目根目录/
├── src/                    # 源代码文件
│   ├── index.js
│   ├── router.js
│   ├── web-server.js
│   ├── apis/
│   ├── utils/
│   └── web/
├── wrangler.toml          # 配置文件
├── package.json           # 依赖文件
├── README.md              # 主文档
├── DEPLOY.md              # 部署指南
├── API.md                 # API文档
├── TROUBLESHOOTING.md     # 故障排除
├── example.env            # 环境变量示例
└── MusicApi/              # 新的Workers目录
    ├── src/               # 需要复制源文件到这里
    ├── wrangler.toml      # 已创建
    ├── package.json       # 已创建
    └── README.md          # 已创建
```

### 目标文件结构

```
MusicApi/
├── src/
│   ├── index.js           # ✅ 已复制
│   ├── router.js          # ✅ 已复制
│   ├── web-server.js      # ❌ 需要复制
│   ├── apis/
│   │   ├── netease.js     # ❌ 需要复制
│   │   └── qq-music.js    # ❌ 需要复制
│   ├── utils/
│   │   ├── index.js       # ❌ 需要复制
│   │   ├── url-parser.js  # ❌ 需要复制
│   │   ├── crypto.js      # ❌ 需要复制
│   │   ├── cookie-manager.js # ❌ 需要复制
│   │   └── http-client.js # ❌ 需要复制
│   └── web/
│       ├── interface.html # ❌ 需要复制
│       ├── styles.css     # ❌ 需要复制
│       └── script.js      # ❌ 需要复制
├── wrangler.toml          # ✅ 已创建
├── package.json           # ✅ 已创建
├── README.md              # ✅ 已创建
├── .gitignore             # ✅ 已创建
├── deploy.sh              # ✅ 已创建
└── SETUP.md               # ✅ 当前文件
```

## 🔄 文件复制步骤

### 方式一：使用命令行（推荐）

如果您在Linux/macOS或Windows的Git Bash中：

```bash
# 进入MusicApi目录
cd MusicApi

# 复制web-server.js
cp ../src/web-server.js ./src/

# 创建并复制apis目录
mkdir -p ./src/apis
cp ../src/apis/netease.js ./src/apis/
cp ../src/apis/qq-music.js ./src/apis/

# 创建并复制utils目录
mkdir -p ./src/utils
cp ../src/utils/index.js ./src/utils/
cp ../src/utils/url-parser.js ./src/utils/
cp ../src/utils/crypto.js ./src/utils/
cp ../src/utils/cookie-manager.js ./src/utils/
cp ../src/utils/http-client.js ./src/utils/

# 创建并复制web目录
mkdir -p ./src/web
cp ../src/web/interface.html ./src/web/
cp ../src/web/styles.css ./src/web/
cp ../src/web/script.js ./src/web/
```

### 方式二：使用Windows命令行

如果您在Windows命令提示符中：

```cmd
# 进入MusicApi目录
cd MusicApi

# 复制web-server.js
copy ..\src\web-server.js .\src\

# 创建并复制apis目录
mkdir .\src\apis
copy ..\src\apis\netease.js .\src\apis\
copy ..\src\apis\qq-music.js .\src\apis\

# 创建并复制utils目录
mkdir .\src\utils
copy ..\src\utils\index.js .\src\utils\
copy ..\src\utils\url-parser.js .\src\utils\
copy ..\src\utils\crypto.js .\src\utils\
copy ..\src\utils\cookie-manager.js .\src\utils\
copy ..\src\utils\http-client.js .\src\utils\

# 创建并复制web目录
mkdir .\src\web
copy ..\src\web\interface.html .\src\web\
copy ..\src\web\styles.css .\src\web\
copy ..\src\web\script.js .\src\web\
```

### 方式三：手动复制

1. 打开文件管理器
2. 导航到项目根目录的 `src` 文件夹
3. 选择以下文件和文件夹：
   - `web-server.js`
   - `apis` 文件夹（包含所有内容）
   - `utils` 文件夹（包含所有内容）
   - `web` 文件夹（包含所有内容）
4. 复制这些文件和文件夹
5. 导航到 `MusicApi/src` 目录
6. 粘贴所有文件和文件夹

## ✅ 验证文件复制

复制完成后，请验证 `MusicApi/src` 目录包含以下文件：

```bash
# 在MusicApi目录中运行
find src -type f -name "*.js" -o -name "*.html" -o -name "*.css" | sort
```

应该显示：
```
src/apis/netease.js
src/apis/qq-music.js
src/index.js
src/router.js
src/utils/cookie-manager.js
src/utils/crypto.js
src/utils/http-client.js
src/utils/index.js
src/utils/url-parser.js
src/web-server.js
src/web/interface.html
src/web/script.js
src/web/styles.css
```

## 🚀 完成设置后的部署

文件复制完成后，您就可以开始部署了：

```bash
# 进入MusicApi目录
cd MusicApi

# 安装依赖
npm install

# 登录Cloudflare
wrangler login

# 设置环境变量
wrangler secret put NETEASE_COOKIE
wrangler secret put QQ_COOKIE

# 部署
npm run deploy

# 或使用部署脚本
chmod +x deploy.sh
./deploy.sh
```

## 🔧 故障排除

### 文件未找到错误

如果在部署时遇到 "Module not found" 错误：

1. 检查文件路径是否正确
2. 确认所有文件都已复制到正确位置
3. 验证文件名大小写是否正确

### 权限错误

如果在Linux/macOS上遇到权限错误：

```bash
# 给部署脚本执行权限
chmod +x deploy.sh

# 如果需要，修改文件所有者
sudo chown -R $USER:$USER MusicApi/
```

### 路径问题

确保您在正确的目录中执行命令：

```bash
# 检查当前目录
pwd

# 应该显示类似：/path/to/your/project/MusicApi
```

## 📞 需要帮助？

如果在设置过程中遇到问题：

1. 检查 [故障排除指南](../TROUBLESHOOTING.md)
2. 查看 [部署指南](../DEPLOY.md)
3. 提交 [GitHub Issue](https://github.com/your-repo/issues)

## 🎉 设置完成

文件整理完成后，您的 `MusicApi` 目录就是一个完整的、可独立部署的Cloudflare Workers项目了！
