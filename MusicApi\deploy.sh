#!/bin/bash

# 音乐解析工具 - 部署脚本
# 用于快速部署到Cloudflare Workers

echo "🎵 音乐解析工具 - Cloudflare Workers 部署脚本"
echo "================================================"

# 检查是否安装了wrangler
if ! command -v wrangler &> /dev/null; then
    echo "❌ Wrangler CLI 未安装"
    echo "请运行: npm install -g wrangler"
    exit 1
fi

# 检查是否已登录
if ! wrangler whoami &> /dev/null; then
    echo "🔐 请先登录 Cloudflare"
    wrangler login
fi

# 检查是否存在必要文件
if [ ! -f "wrangler.toml" ]; then
    echo "❌ 未找到 wrangler.toml 配置文件"
    exit 1
fi

if [ ! -f "package.json" ]; then
    echo "❌ 未找到 package.json 文件"
    exit 1
fi

if [ ! -d "src" ]; then
    echo "❌ 未找到 src 目录"
    echo "请确保已将所有源文件复制到 src/ 目录中"
    exit 1
fi

# 询问部署环境
echo ""
echo "请选择部署环境:"
echo "1) 开发环境 (development)"
echo "2) 测试环境 (staging)"
echo "3) 生产环境 (production)"
echo "4) 默认环境"
read -p "请输入选择 (1-4): " choice

case $choice in
    1)
        ENV="development"
        ;;
    2)
        ENV="staging"
        ;;
    3)
        ENV="production"
        ;;
    4)
        ENV=""
        ;;
    *)
        echo "❌ 无效选择"
        exit 1
        ;;
esac

# 检查环境变量配置
echo ""
echo "🔧 检查环境变量配置..."

# 检查是否设置了必要的secrets
SECRETS=$(wrangler secret list 2>/dev/null | grep -E "(NETEASE_COOKIE|QQ_COOKIE)" | wc -l)

if [ $SECRETS -lt 2 ]; then
    echo "⚠️  检测到缺少必要的环境变量"
    echo ""
    echo "请设置以下环境变量:"
    echo "1. NETEASE_COOKIE - 网易云音乐Cookie (需要黑胶会员)"
    echo "2. QQ_COOKIE - QQ音乐Cookie (需要绿钻会员)"
    echo ""
    read -p "是否现在设置环境变量? (y/n): " setup_vars
    
    if [ "$setup_vars" = "y" ] || [ "$setup_vars" = "Y" ]; then
        echo ""
        echo "设置网易云音乐Cookie:"
        wrangler secret put NETEASE_COOKIE
        
        echo ""
        echo "设置QQ音乐Cookie:"
        wrangler secret put QQ_COOKIE
    else
        echo "⚠️  跳过环境变量设置，部署可能会失败"
    fi
fi

# 开始部署
echo ""
echo "🚀 开始部署..."

if [ -z "$ENV" ]; then
    echo "部署到默认环境..."
    wrangler deploy
else
    echo "部署到 $ENV 环境..."
    wrangler deploy --env $ENV
fi

# 检查部署结果
if [ $? -eq 0 ]; then
    echo ""
    echo "✅ 部署成功!"
    echo ""
    echo "🌐 访问地址:"
    
    if [ -z "$ENV" ]; then
        WORKER_NAME=$(grep "name" wrangler.toml | head -1 | cut -d'"' -f2)
    else
        WORKER_NAME=$(grep -A5 "\[env\.$ENV\]" wrangler.toml | grep "name" | cut -d'"' -f2)
    fi
    
    if [ ! -z "$WORKER_NAME" ]; then
        echo "https://$WORKER_NAME.your-subdomain.workers.dev"
    fi
    
    echo ""
    echo "📖 可用接口:"
    echo "- Web界面: /"
    echo "- 健康检查: /health"
    echo "- API信息: /info"
    echo "- 网易云搜索: /api/netease/search"
    echo "- 网易云解析: /api/netease/song"
    echo "- QQ音乐解析: /api/qq/song"
    echo ""
    echo "📚 详细文档请查看 README.md"
    
else
    echo ""
    echo "❌ 部署失败!"
    echo "请检查错误信息并重试"
    exit 1
fi

echo ""
echo "🎉 部署完成!"
