/**
 * 音乐解析工具 - Cloudflare Workers版本
 * 支持网易云音乐和QQ音乐解析
 */

export default {
  async fetch(request, env, ctx) {
    try {
      const url = new URL(request.url);
      
      // 添加CORS头
      const corsHeaders = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      };

      // 处理OPTIONS预检请求
      if (request.method === 'OPTIONS') {
        return new Response(null, { headers: corsHeaders });
      }

      // 健康检查
      if (url.pathname === '/health') {
        return new Response(JSON.stringify({
          status: 'ok',
          version: env.APP_VERSION || '1.0.0',
          timestamp: new Date().toISOString()
        }), {
          headers: { 
            'Content-Type': 'application/json',
            ...corsHeaders 
          }
        });
      }

      // 根路径返回欢迎信息
      if (url.pathname === '/') {
        return new Response(JSON.stringify({
          message: '音乐解析工具 API',
          version: env.APP_VERSION || '1.0.0',
          endpoints: {
            health: '/health',
            netease: '/api/netease/*',
            qq: '/api/qq/*'
          }
        }), {
          headers: { 
            'Content-Type': 'application/json',
            ...corsHeaders 
          }
        });
      }

      // 暂时返回404，后续会添加路由处理
      return new Response('Not Found', { 
        status: 404,
        headers: corsHeaders
      });

    } catch (error) {
      return new Response(JSON.stringify({
        error: 'Internal Server Error',
        message: error.message
      }), {
        status: 500,
        headers: { 
          'Content-Type': 'application/json'
        }
      });
    }
  },
};
