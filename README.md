# 音乐解析工具 - Cloudflare Workers版本

> 将网易云音乐和QQ音乐解析功能整合到Cloudflare Workers平台，提供高性能的全球边缘计算音乐解析服务。

## 功能特性

- 🎵 **网易云音乐解析**：支持单曲、搜索、歌单、专辑解析
- 🎶 **QQ音乐解析**：支持多种音质格式的歌曲解析
- 🌐 **全球加速**：基于Cloudflare Workers的边缘计算
- 📱 **响应式界面**：现代化Web界面，支持移动端
- 🔒 **安全可靠**：无服务器架构，数据不存储
- ⚡ **高性能**：毫秒级响应，全球CDN分发

## 支持的音质

### 网易云音乐
- `standard`：标准音质
- `exhigh`：极高音质  
- `lossless`：无损音质
- `hires`：Hi-Res音质
- `jyeffect`：高清环绕声
- `sky`：沉浸环绕声
- `jymaster`：超清母带

### QQ音乐
- `128`、`320`：MP3格式
- `flac`：无损格式
- `m4a`：AAC格式
- `master`：母带音质
- `atmos`：杜比全景声

## 快速开始

### 1. 环境准备

```bash
# 安装依赖
npm install

# 配置环境变量
cp example.env .env
```

### 2. 本地开发

```bash
# 启动开发服务器
npm run dev
```

### 3. 部署到Cloudflare

```bash
# 部署到生产环境
npm run deploy:production
```

## API接口

### 网易云音乐

- `GET /api/netease/song` - 单曲解析
- `GET /api/netease/search` - 搜索歌曲
- `GET /api/netease/playlist` - 歌单解析
- `GET /api/netease/album` - 专辑解析

### QQ音乐

- `GET /api/qq/song` - 歌曲解析

## 配置说明

在 `wrangler.toml` 中配置必要的环境变量：

```toml
[vars]
NETEASE_COOKIE = "你的网易云音乐Cookie"
QQ_COOKIE = "你的QQ音乐Cookie"
```

## 注意事项

- 需要有效的会员Cookie才能解析高音质资源
- 请遵守相关平台的使用条款
- 仅供学习和个人使用，禁止商业用途

## 许可证

MIT License

## 致谢

基于以下开源项目：
- [Netease_url](https://github.com/Suxiaoqinx/Netease_url)
- [tencent_url](https://github.com/Suxiaoqinx/tencent_url)
