# 音乐解析工具 - Cloudflare Workers版本

> 将网易云音乐和QQ音乐解析功能整合到Cloudflare Workers平台，提供高性能的全球边缘计算音乐解析服务。

[![Deploy to Cloudflare Workers](https://deploy.workers.cloudflare.com/button)](https://deploy.workers.cloudflare.com/?url=https://github.com/your-repo/music-parser)

## 🚀 快速开始

**重要提示**：Cloudflare Workers的部署文件已整理到 `MusicApi` 目录中。

```bash
# 进入Workers项目目录
cd MusicApi

# 查看详细的部署说明
cat README.md
```

## 📁 项目结构

```
music-parser/
├── MusicApi/              # 🎯 Cloudflare Workers项目目录
│   ├── src/               # 源代码
│   ├── wrangler.toml      # Workers配置
│   ├── package.json       # 项目依赖
│   ├── README.md          # Workers部署说明
│   ├── SETUP.md           # 文件整理指南
│   └── deploy.sh          # 部署脚本
├── src/                   # 原始源代码文件
├── DEPLOY.md              # 详细部署指南
├── API.md                 # API使用文档
├── TROUBLESHOOTING.md     # 故障排除指南
├── example.env            # 环境变量示例
└── README.md              # 本文件
```

## ✨ 功能特性

- 🎵 **网易云音乐解析**：支持单曲、搜索、歌单、专辑解析
- 🎶 **QQ音乐解析**：支持多种音质格式的歌曲解析
- 🌐 **全球加速**：基于Cloudflare Workers的边缘计算
- 📱 **现代化界面**：响应式Web界面，支持移动端和主题切换
- 🔒 **安全可靠**：无服务器架构，数据不存储
- ⚡ **高性能**：毫秒级响应，全球CDN分发
- 🎨 **美观设计**：玻璃态效果，现代化UI设计
- 🔄 **API兼容**：与原Python版本100%兼容

## 🎼 支持的音质

### 网易云音乐 (7种音质等级)
- `standard`：标准音质 (128kbps)
- `exhigh`：极高音质 (320kbps)
- `lossless`：无损音质 (FLAC)
- `hires`：Hi-Res音质 (24bit/96kHz)
- `jyeffect`：高清环绕声
- `sky`：沉浸环绕声
- `jymaster`：超清母带

### QQ音乐 (13种格式支持)
- **MP3格式**：`128`、`320`
- **无损格式**：`flac`、`master`
- **AAC格式**：`aac_48`、`aac_96`、`aac_192`
- **OGG格式**：`ogg_96`、`ogg_192`、`ogg_320`、`ogg_640`
- **环绕声**：`atmos_2`、`atmos_51`

## 🚀 快速开始

### 方式一：一键部署（推荐）

```bash
# 克隆项目
git clone https://github.com/your-repo/music-parser.git
cd music-parser/MusicApi

# 一键部署
chmod +x deploy.sh
./deploy.sh
```

### 方式二：手动部署

```bash
# 进入Workers项目目录
cd MusicApi

# 安装Wrangler CLI
npm install -g wrangler

# 登录Cloudflare
wrangler login

# 设置环境变量
wrangler secret put NETEASE_COOKIE
wrangler secret put QQ_COOKIE

# 部署
npm run deploy
```

### 文件整理

如果您需要手动整理文件结构，请查看：
- 📋 **[文件整理指南](./MusicApi/SETUP.md)** - 如何将源文件复制到MusicApi目录

## 📖 详细文档

- 🎯 **[Workers项目说明](./MusicApi/README.md)** - Cloudflare Workers项目详细说明
- 📋 **[文件整理指南](./MusicApi/SETUP.md)** - 如何整理项目文件结构
- 📚 **[部署指南](./DEPLOY.md)** - 详细的部署步骤和配置说明
- 🔧 **[API文档](./API.md)** - 完整的API接口说明和使用示例
- 🆘 **[故障排除](./TROUBLESHOOTING.md)** - 常见问题和解决方案
- ⚙️ **[配置示例](./example.env)** - 环境变量配置模板

## 🌐 API接口

### 网易云音乐API

| 接口 | 方法 | 说明 | 示例 |
|------|------|------|------|
| `/api/netease/search` | GET | 搜索歌曲 | `?keywords=周杰伦&limit=10` |
| `/api/netease/song` | POST | 单曲解析 | `{"url":"186016","level":"lossless","type":"json"}` |
| `/api/netease/playlist` | GET | 歌单解析 | `?id=123456789` |
| `/api/netease/album` | GET | 专辑解析 | `?id=18905` |

### QQ音乐API

| 接口 | 方法 | 说明 | 示例 |
|------|------|------|------|
| `/api/qq/song` | GET | 歌曲解析 | `?url=https://y.qq.com/n/ryqq/songDetail/xxx` |

### 系统接口

| 接口 | 方法 | 说明 |
|------|------|------|
| `/health` | GET | 健康检查 |
| `/info` | GET | API信息 |
| `/` | GET | Web界面 |

## 🎨 界面预览

### 桌面端
- 🌈 现代化玻璃态设计
- 🎯 双平台切换（网易云/QQ音乐）
- 🎵 多功能模块（搜索/解析/歌单/专辑）
- 🎨 主题切换（亮色/暗色）

### 移动端
- 📱 完美适配各种屏幕尺寸
- 👆 触摸优化的交互设计
- ⚡ 流畅的动画效果
- 🎵 浮动音乐播放器

## 🔧 高级配置

### 自定义域名

```bash
# 添加自定义域名
wrangler route add "music.yourdomain.com/*" music-parser
```

### 多环境部署

```bash
# 开发环境
wrangler dev

# 测试环境
wrangler deploy --env staging

# 生产环境
wrangler deploy --env production
```

### 性能监控

```bash
# 查看实时日志
wrangler tail

# 查看部署状态
wrangler status
```

## 🔒 安全说明

### Cookie获取和配置
1. **网易云音乐**：需要黑胶会员Cookie
2. **QQ音乐**：需要绿钻会员Cookie
3. **安全建议**：
   - 定期更换Cookie
   - 使用Wrangler Secrets存储敏感信息
   - 不要在代码中硬编码Cookie

### 使用限制
- 仅供学习和个人使用
- 请遵守相关平台的使用条款
- 禁止商业用途和大规模爬取
- 建议控制请求频率

## 💰 成本说明

### Cloudflare Workers免费额度
- **请求数**：100,000次/天
- **CPU时间**：10ms/请求
- **内存**：128MB

### 预估使用成本
- **个人使用**：通常在免费额度内
- **小型项目**：约$5-10/月
- **中型项目**：约$20-50/月

## 🤝 贡献指南

欢迎提交Issue和Pull Request！

1. Fork本项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🙏 致谢

本项目基于以下优秀的开源项目：
- [Netease_url](https://github.com/Suxiaoqinx/Netease_url) - 网易云音乐解析
- [tencent_url](https://github.com/Suxiaoqinx/tencent_url) - QQ音乐解析

## ⭐ Star History

如果这个项目对您有帮助，请给个Star支持一下！

[![Star History Chart](https://api.star-history.com/svg?repos=your-repo/music-parser&type=Date)](https://star-history.com/#your-repo/music-parser&Date)
