/**
 * 音乐解析工具 - Cloudflare Workers版本
 * 支持网易云音乐和QQ音乐解析
 */

import { APIRouter } from './router.js';
import { WebServer } from './web-server.js';

export default {
  async fetch(request, env, ctx) {
    try {
      const url = new URL(request.url);

      // API请求路由到APIRouter处理
      if (url.pathname.startsWith('/api/') || url.pathname === '/health') {
        return await APIRouter.handleRequest(request, env);
      }

      // Web界面请求路由到WebServer处理
      if (url.pathname === '/' || url.pathname.startsWith('/web/')) {
        return await WebServer.handleWebRequest(request, env);
      }

      // 兼容原版路由 - 重定向到新API
      const legacyRoutes = {
        '/Song_V1': '/api/netease/song',
        '/Search': '/api/netease/search',
        '/Playlist': '/api/netease/playlist',
        '/Album': '/api/netease/album',
        '/song': '/api/qq/song'
      };

      if (legacyRoutes[url.pathname]) {
        // 构建新的URL，保持查询参数
        const newUrl = new URL(request.url);
        newUrl.pathname = legacyRoutes[url.pathname];
        
        // 创建新的请求对象
        const newRequest = new Request(newUrl.toString(), {
          method: request.method,
          headers: request.headers,
          body: request.body
        });
        
        return await APIRouter.handleRequest(newRequest, env);
      }

      // API信息接口
      if (url.pathname === '/info') {
        return new Response(JSON.stringify({
          message: '音乐解析工具 API',
          version: env.APP_VERSION || '1.0.0',
          endpoints: {
            web: '/',
            health: '/health',
            netease: {
              song: '/api/netease/song',
              search: '/api/netease/search', 
              playlist: '/api/netease/playlist',
              album: '/api/netease/album'
            },
            qq: {
              song: '/api/qq/song'
            }
          },
          compatibility: {
            netease_legacy: {
              song: '/Song_V1',
              search: '/Search',
              playlist: '/Playlist', 
              album: '/Album'
            },
            qq_legacy: {
              song: '/song'
            }
          }
        }), {
          headers: { 
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          }
        });
      }

      // 404处理 - 返回Web界面的404页面
      return await WebServer.serve404Page();

    } catch (error) {
      return new Response(JSON.stringify({
        error: 'Internal Server Error',
        message: error.message,
        timestamp: new Date().toISOString()
      }), {
        status: 500,
        headers: { 
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        }
      });
    }
  },
};
