# 音乐解析工具 - 部署指南

> 详细的Cloudflare Workers部署说明，支持多种部署方式

## 📋 部署前准备

### 1. 环境要求

- **Node.js**: 16.0.0 或更高版本
- **npm**: 7.0.0 或更高版本
- **Cloudflare账户**: 免费账户即可

### 2. 获取必要的Cookie

#### 网易云音乐Cookie获取
1. 登录 [网易云音乐网页版](https://music.163.com/)
2. 确保账户有**黑胶会员**权限（解析高音质必需）
3. 打开浏览器开发者工具 (F12)
4. 切换到 Network 标签
5. 刷新页面，找到任意请求
6. 复制 Cookie 值（完整的Cookie字符串）

#### QQ音乐Cookie获取
1. 登录 [QQ音乐网页版](https://y.qq.com/)
2. 确保账户有**会员**权限（解析高音质必需）
3. 打开浏览器开发者工具 (F12)
4. 切换到 Network 标签
5. 刷新页面，找到任意请求
6. 复制 Cookie 值（完整的Cookie字符串）

## 🚀 部署方式

### 方式一：使用Wrangler CLI（推荐）

#### 1. 安装Wrangler
```bash
npm install -g wrangler
```

#### 2. 登录Cloudflare
```bash
wrangler login
```

#### 3. 克隆项目
```bash
git clone <your-repo-url>
cd music-parser
```

#### 4. 安装依赖
```bash
npm install
```

#### 5. 配置环境变量

**选项A：使用wrangler.toml文件**
编辑 `wrangler.toml` 文件：
```toml
[vars]
NETEASE_COOKIE = "your_netease_cookie_here"
QQ_COOKIE = "your_qq_cookie_here"
```

**选项B：使用Wrangler命令行（推荐）**
```bash
# 设置网易云Cookie
wrangler secret put NETEASE_COOKIE
# 输入您的网易云Cookie

# 设置QQ音乐Cookie
wrangler secret put QQ_COOKIE
# 输入您的QQ音乐Cookie
```

#### 6. 部署到生产环境
```bash
# 部署到默认环境
wrangler deploy

# 或部署到指定环境
wrangler deploy --env production
```

### 方式二：使用Cloudflare控制台

#### 1. 准备代码
将所有 `src/` 目录下的文件内容合并到一个文件中，或使用构建工具打包。

#### 2. 登录Cloudflare控制台
访问 [Cloudflare Workers](https://workers.cloudflare.com/)

#### 3. 创建新的Worker
1. 点击 "Create a Service"
2. 输入服务名称（如：music-parser）
3. 选择 "HTTP handler" 模板

#### 4. 上传代码
1. 在代码编辑器中粘贴您的代码
2. 点击 "Save and Deploy"

#### 5. 配置环境变量
1. 在Worker详情页面，点击 "Settings" 标签
2. 找到 "Environment Variables" 部分
3. 添加以下变量：
   - `NETEASE_COOKIE`: 您的网易云音乐Cookie
   - `QQ_COOKIE`: 您的QQ音乐Cookie
   - `APP_VERSION`: 1.0.0

#### 6. 配置自定义域名（可选）
1. 在 "Triggers" 标签中
2. 点击 "Add Custom Domain"
3. 输入您的域名

## 🔧 高级配置

### 环境变量说明

| 变量名 | 必需 | 说明 | 示例 |
|--------|------|------|------|
| `NETEASE_COOKIE` | 是 | 网易云音乐Cookie | `MUSIC_U=xxx; __csrf=xxx;` |
| `QQ_COOKIE` | 是 | QQ音乐Cookie | `uin=xxx; skey=xxx;` |
| `APP_VERSION` | 否 | 应用版本号 | `1.0.0` |

### 多环境部署

#### 开发环境
```bash
wrangler dev
```

#### 测试环境
```bash
wrangler deploy --env staging
```

#### 生产环境
```bash
wrangler deploy --env production
```

### 自定义域名配置

#### 1. 在Cloudflare中添加域名
确保您的域名已添加到Cloudflare并且DNS已生效。

#### 2. 配置路由
```bash
wrangler route add "music.yourdomain.com/*" music-parser-prod
```

#### 3. 更新wrangler.toml
```toml
[env.production]
name = "music-parser-prod"
routes = [
  { pattern = "music.yourdomain.com/*", zone_name = "yourdomain.com" }
]
```

## 📊 部署验证

### 1. 健康检查
访问 `https://your-worker.your-subdomain.workers.dev/health`

预期响应：
```json
{
  "status": "ok",
  "version": "1.0.0",
  "timestamp": "2025-01-01T00:00:00.000Z",
  "services": {
    "netease": true,
    "qq": true
  }
}
```

### 2. API测试
```bash
# 测试网易云搜索
curl "https://your-worker.your-subdomain.workers.dev/api/netease/search?keywords=周杰伦"

# 测试QQ音乐解析
curl "https://your-worker.your-subdomain.workers.dev/api/qq/song?url=https://y.qq.com/n/ryqq/songDetail/xxx"
```

### 3. Web界面测试
直接访问 `https://your-worker.your-subdomain.workers.dev/`

## 🔄 更新部署

### 代码更新
```bash
# 拉取最新代码
git pull origin main

# 重新部署
wrangler deploy
```

### 环境变量更新
```bash
# 更新Cookie
wrangler secret put NETEASE_COOKIE
wrangler secret put QQ_COOKIE
```

## 📈 监控和日志

### 查看实时日志
```bash
wrangler tail
```

### 查看部署状态
```bash
wrangler status
```

### 性能监控
在Cloudflare控制台的Analytics标签中查看：
- 请求数量
- 响应时间
- 错误率
- 带宽使用

## 💰 成本估算

### Cloudflare Workers免费额度
- **请求数**: 100,000 次/天
- **CPU时间**: 10ms/请求
- **内存**: 128MB

### 超出免费额度的收费
- **请求数**: $0.50/百万请求
- **CPU时间**: $12.50/百万GB-s

### 成本优化建议
1. 启用缓存减少重复请求
2. 优化代码减少CPU使用时间
3. 使用CDN缓存静态资源

## 🔒 安全建议

### 1. Cookie安全
- 定期更换Cookie
- 不要在公共代码仓库中暴露Cookie
- 使用Wrangler secrets而不是环境变量

### 2. 访问控制
```javascript
// 可选：添加IP白名单
const allowedIPs = ['your.ip.address'];
if (!allowedIPs.includes(request.headers.get('CF-Connecting-IP'))) {
  return new Response('Forbidden', { status: 403 });
}
```

### 3. 速率限制
```javascript
// 可选：添加速率限制
const rateLimiter = new Map();
const clientIP = request.headers.get('CF-Connecting-IP');
// 实现速率限制逻辑
```

## 🆘 故障排除

常见问题和解决方案请参考 [故障排除指南](./TROUBLESHOOTING.md)。

## 📞 技术支持

如果遇到部署问题，请：
1. 检查 [故障排除指南](./TROUBLESHOOTING.md)
2. 查看 [API文档](./API.md)
3. 提交 [GitHub Issue](https://github.com/your-repo/issues)
